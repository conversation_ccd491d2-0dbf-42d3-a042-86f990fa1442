import { <PERSON><PERSON><PERSON><PERSON> } from 'bson';
import dayjs from 'dayjs';
import getRedisInstance from '../../../core/redis';
import sendSMS from '../../../core/sms';
import { generateToken } from '../../../core/tokens';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { createOTPToken } from '../../../utils';
import { getUserCompanies } from '../../../utils/getUserCompanies';
import { GraphQLResetPasswordLinkResolvers } from '../definitions';
import { isRequiredSmsOtpValidation } from '../mutations/authentication/authenticate';

export type OTP = {
    code: string;
    generatedAt: Date;
    expiresAt: Date;
};

const ResetPasswordLinkGraphQL: GraphQLResetPasswordLinkResolvers = {
    token: parent =>
        generateToken(
            'resetPassword',
            {
                userId: parent.data.userId,
                linkId: parent._id,
            },
            10 * 60
        ),
    verified: async (root, _args, { getTranslations }) => {
        const { collections } = await getDatabaseContext();
        const { t } = await getTranslations(['notifications']);

        const {
            data: { userId },
        } = root;
        const requiredSmsOtpValidation = await isRequiredSmsOtpValidation(userId);

        if (requiredSmsOtpValidation) {
            const channel = userId.toHexString();
            const redis = getRedisInstance();
            const redisKey = `otp:resetPassword:${channel}`;
            const serializedMessage = await redis.get(redisKey);

            const message = serializedMessage ? (EJSON.deserialize(JSON.parse(serializedMessage)) as OTP) : null;

            if (!message || dayjs(message.generatedAt).add(5, 'minutes').isBefore(dayjs())) {
                const payload = await createOTPToken(userId, channel, 10, 'resetPassword');
                const message = t('notifications:authenticate', payload);

                const user = await collections.users.findOne({ _id: userId });
                const companies = await getUserCompanies(user._id);

                await sendSMS(companies[0], user.mobile, message);
            }

            return false;
        }

        return true;
    },
    email: async root => {
        const { collections } = await getDatabaseContext();
        const {
            data: { userId },
        } = root;

        const user = await collections.users.findOne({ _id: userId });

        if (!user) {
            throw new Error('User not found');
        }

        return user.email;
    },
};

export default ResetPasswordLinkGraphQL;
