import { isEmpty } from 'lodash/fp';
import qs from 'qs';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import urlJoin from 'url-join';
import { useRetrieveLinkQuery } from '../api/queries/retrieveLink';
import { ApplicationKind, FinderVehicle, ModelConfigurator } from '../api/types';
import PortalLoadingElement from '../components/PortalLoadingElement';
import { useAccountContext } from '../components/contexts/AccountContextManager';
import InternalErrorResult from '../components/results/InternalErrorResult';
import InvalidLinkResult from '../components/results/InvalidLinkResult';
import NotFoundResult from '../components/results/NotFoundResult';
import InvalidBookingResult from './portal/MobilityApplicationEntrypoint/ThankYouPage/InvalidBookingResult';
import { JourneyStage } from './portal/StandardApplicationEntrypoint/Journey/JourneyController';
import RedirectAndReplace from './shared/RedirectAndReplace';

type MyInfoQueryString = { code: string };

const ExternalLinkPage = () => {
    const { id } = useParams<{ id: string }>();
    const { data, loading, error } = useRetrieveLinkQuery({ fetchPolicy: 'no-cache', variables: { id } });
    const { logout } = useAccountContext();

    const response = data?.retrieveLink;

    useEffect(() => {
        switch (response?.__typename) {
            case 'ResetPasswordLink':
            case 'CreateNewUserLink':
            case 'VerifyEmailUpdateLink': {
                logout();
            }
        }
    }, [logout, response?.__typename]);

    const router = response && 'router' in response ? response.router : null;
    const shouldReplaceHostname = !isEmpty(router?.hostname) && window.location.hostname !== router.hostname;

    useEffect(() => {
        if (shouldReplaceHostname) {
            const target = new URL(window.location.href);
            target.hostname = router?.hostname;
            target.pathname = urlJoin(router?.pathname, `l/${id}`);

            window.location.replace(target);
        }
    }, [router, shouldReplaceHostname, id]);

    if (error) {
        return <InternalErrorResult />;
    }

    if (loading || shouldReplaceHostname) {
        return <PortalLoadingElement />;
    }

    if (!response) {
        return <InvalidLinkResult />;
    }

    switch (response.__typename) {
        case 'ResetPasswordLink':
            return (
                <RedirectAndReplace
                    path="/auth/resetPassword"
                    state={{ token: response.token, verified: response.verified, email: response.email }}
                />
            );

        case 'CreateNewUserLink':
            return (
                <RedirectAndReplace
                    path="/auth/activate"
                    state={{ token: response.token, verified: response.verified }}
                />
            );

        case 'MyInfoCallbackLink': {
            const queryString = qs.parse(window.location.search, {
                ignoreQueryPrefix: true,
            }) as MyInfoQueryString;
            switch (response.applicationJourney.application.__typename) {
                case 'StandardApplication': {
                    return (
                        <RedirectAndReplace
                            path={urlJoin(
                                ...[
                                    response.endpoint.pathname || '/',
                                    response.applicationJourney.application.withCustomerDevice && 'remote',
                                    'apply',
                                ].filter(Boolean)
                            )}
                            state={{
                                token: response.applicationJourney.token,
                                myInfoAuthorizationCode: queryString.code,
                                linkId: response.linkId,
                            }}
                        />
                    );
                }
                case 'EventApplication':
                case 'ConfiguratorApplication':
                    return (
                        <RedirectAndReplace
                            path={urlJoin(response.endpoint.pathname || '/', 'apply')}
                            state={{
                                token: response.applicationJourney.token,
                                myInfoAuthorizationCode: queryString.code,
                                linkId: response.linkId,
                            }}
                        />
                    );

                case 'FinderApplication': {
                    const finderVehicleListingId =
                        response.applicationJourney.application.vehicle.__typename === 'FinderVehicle'
                            ? response.applicationJourney.application.vehicle.listing.id
                            : undefined;

                    if (!finderVehicleListingId) {
                        return <NotFoundResult />;
                    }

                    return (
                        <RedirectAndReplace
                            path={urlJoin(
                                ...[
                                    response.endpoint.pathname,
                                    response.applicationJourney.application.withCustomerDevice && 'remote',
                                    finderVehicleListingId,
                                    'apply',
                                ].filter(Boolean)
                            )}
                            state={{
                                token: response.applicationJourney.token,
                                myInfoAuthorizationCode: queryString.code,
                                linkId: response.linkId,
                            }}
                        />
                    );
                }

                default:
                    throw new Error('Invalid Error');
            }
        }

        case 'ConfiguratorApplicationLink': {
            if (response.applicationJourney.application.__typename === 'ConfiguratorApplication') {
                if (response.applicationJourney.application.draftFlow.isReceived) {
                    return <NotFoundResult />;
                }

                return (
                    <RedirectAndReplace
                        path={urlJoin(response.endpoint.pathname, 'apply')}
                        state={{ token: response.applicationJourney.token }}
                    />
                );
            }

            return (
                <RedirectAndReplace
                    path={urlJoin(response.endpoint.pathname, 'details', response.urlIdentifier)}
                    state={{
                        token: response.applicationJourney.token,
                    }}
                />
            );
        }

        // This is for applying for financing/insurance from application details
        case 'StandardApplicationLink': {
            return (
                <RedirectAndReplace
                    path={urlJoin(response.endpoint.pathname, 'apply')}
                    state={{
                        token: response.applicationJourney.token,
                    }}
                />
            );
        }

        case 'EventApplicationLink': {
            return (
                <RedirectAndReplace
                    path={urlJoin(response.endpoint.pathname, 'apply')}
                    state={{
                        token: response.applicationJourney.token,
                    }}
                />
            );
        }

        case 'FinderApplicationLink': {
            const finderVehicleListingId =
                response.applicationJourney.application.__typename === 'FinderApplication' &&
                response.applicationJourney.application.vehicle.__typename === 'FinderVehicle'
                    ? response.applicationJourney.application.vehicle.listing.id
                    : undefined;

            if (!finderVehicleListingId) {
                return <NotFoundResult />;
            }

            return (
                <RedirectAndReplace
                    path={urlJoin(response.endpoint.pathname, finderVehicleListingId, 'apply')}
                    state={{
                        token: response.applicationJourney.token,
                    }}
                />
            );
        }

        case 'NamirialSigningLink': {
            return (
                <RedirectAndReplace
                    path={response.path}
                    state={{
                        token: response.token,
                        stage: JourneyStage.NamirialRedirect,
                    }}
                />
            );
        }

        case 'VerifyEmailUpdateLink':
            return (
                <RedirectAndReplace
                    path="/auth/verifyEmail"
                    state={{ token: response.token, email: response.email, userId: response.userId }}
                />
            );

        case 'ProceedWithCustomerLink': {
            const path = response.applicationKind === ApplicationKind.Event ? 'authorize' : 'remote/authorize';

            return (
                <RedirectAndReplace
                    path={urlJoin(response.endpoint.pathname || '/', path)}
                    state={{
                        secret: response.secret,
                    }}
                />
            );
        }

        case 'MobilityApplicationCancellationLink': {
            if (response.token === 'expired' || !response.path) {
                return (
                    <InvalidBookingResult
                        application={response.application.__typename === 'MobilityApplication' && response.application}
                        isCancel
                    />
                );
            }

            return <RedirectAndReplace path={response.path} state={{ token: response.token, isCancel: true }} />;
        }

        case 'AdyenRedirectionLink':
        case 'PorschePaymentRedirectionLink':
        case 'FiservPaymentRedirectionLink':
        case 'PayGatePaymentRedirectionLink':
        case 'TtbPaymentRedirectionLink':
        case 'GiftVoucherAdyenRedirectionLink':
        case 'GiftVoucherPorschePaymentRedirectionLink':
        case 'GiftVoucherFiservPaymentRedirectionLink':
        case 'GiftVoucherPayGatePaymentRedirectionLink':
        case 'GiftVoucherTtbPaymentRedirectionLink': {
            return <RedirectAndReplace path={response.path} state={{ token: response.token }} />;
        }

        case 'MobilityApplicationAmendmentLink': {
            if (response.token === 'expired' || !response.path) {
                return (
                    <InvalidBookingResult
                        application={response.application.__typename === 'MobilityApplication' && response.application}
                        isCancel={false}
                    />
                );
            }

            return <RedirectAndReplace path={response.path} state={{ token: response.token }} />;
        }
        case 'CTSFinderRedirectionLink': {
            return (
                <RedirectAndReplace
                    path={urlJoin(response.endpoint.pathname, response.vehicleId)}
                    state={{
                        token: response.applicationJourney.token,
                        type: response.type,
                        ctsSetting: response.ctsSetting,
                        applicationJourney: response.applicationJourney,
                    }}
                />
            );
        }
        case 'TestDriveProcessRedirectionLink': {
            switch (response.applicationJourney.application.__typename) {
                case 'FinderApplication':
                case 'StandardApplication':
                case 'EventApplication':
                case 'ConfiguratorApplication':
                case 'LaunchpadApplication': {
                    const { endpoint } = response.applicationJourney.application;

                    if (!endpoint) {
                        return <InternalErrorResult />;
                    }

                    let urlSlug = '';
                    switch (response.applicationJourney.application.__typename) {
                        case 'FinderApplication': {
                            const { vehicle } = response.applicationJourney.application;

                            if (vehicle.__typename !== 'FinderVehicle') {
                                return <InternalErrorResult />;
                            }
                            urlSlug = vehicle.listing.id;
                            break;
                        }

                        case 'EventApplication': {
                            urlSlug = response.applicationJourney.application.event.urlSlug;
                            break;
                        }

                        default:
                            urlSlug = '';
                    }

                    return (
                        <RedirectAndReplace
                            path={urlJoin(endpoint.pathname || '/', urlSlug, 'testDrive')}
                            state={{
                                token: response.applicationJourney.token,
                                stage: JourneyStage.Initialize,
                            }}
                        />
                    );
                }

                default:
                    return <InternalErrorResult />;
            }
        }

        case 'ApplyNewRedirectionLink': {
            const vehicleId = (() => {
                switch (response.applicationJourney.application.__typename) {
                    case 'StandardApplication':
                        return response.applicationJourney.application.vehicleId;

                    case 'FinderApplication':
                        return (response.applicationJourney.application.vehicle as FinderVehicle)?.listing?.id;

                    case 'ConfiguratorApplication':
                        return (
                            response.applicationJourney.application.configurator.modelConfigurator as ModelConfigurator
                        ).urlIdentifier;

                    default:
                        return undefined;
                }
            })();

            if (!vehicleId) {
                return <NotFoundResult />;
            }

            const path = (() => {
                switch (response.applicationJourney.application.__typename) {
                    case 'StandardApplication':
                        return urlJoin(response.endpoint.pathname, 'details', vehicleId);

                    case 'FinderApplication':
                        return urlJoin(response.endpoint.pathname, vehicleId);

                    case 'ConfiguratorApplication':
                        return urlJoin(response.endpoint.pathname, 'details', vehicleId);

                    default:
                        return undefined;
                }
            })();

            return (
                <RedirectAndReplace
                    path={path}
                    state={{
                        token: response.applicationJourney.token,
                        isFromApplyNew: true,
                    }}
                />
            );
        }

        case 'PorscheIdCallbackLink': {
            const queryString = qs.parse(window.location.search, {
                ignoreQueryPrefix: true,
            }) as MyInfoQueryString;

            switch (response.applicationJourney.application.__typename) {
                case 'EventApplication':
                case 'ConfiguratorApplication':
                    return (
                        <RedirectAndReplace
                            path={urlJoin(response.endpoint.pathname || '/', 'apply')}
                            state={{
                                token: response.applicationJourney.token,
                                porscheIdAuthorizationCode: queryString.code,
                                linkId: response.linkId,
                            }}
                        />
                    );

                case 'FinderApplication': {
                    const finderVehicleListingId =
                        response.applicationJourney.application.vehicle.__typename === 'FinderVehicle'
                            ? response.applicationJourney.application.vehicle.listing.id
                            : undefined;

                    if (!finderVehicleListingId) {
                        return <NotFoundResult />;
                    }

                    return (
                        <RedirectAndReplace
                            path={urlJoin(
                                ...[
                                    response.endpoint.pathname,
                                    response.applicationJourney.application.withCustomerDevice && 'remote',
                                    finderVehicleListingId,
                                    'apply',
                                ].filter(Boolean)
                            )}
                            state={{
                                token: response.applicationJourney.token,
                                porscheIdAuthorizationCode: queryString.code,
                                linkId: response.linkId,
                            }}
                        />
                    );
                }

                default:
                    throw new Error('Invalid Error');
            }
        }

        default:
            return <InternalErrorResult />;
    }
};

export default ExternalLinkPage;
