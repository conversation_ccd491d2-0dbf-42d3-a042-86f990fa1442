import { isBoolean } from 'lodash/fp';
import { useMemo, useReducer } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import TokenExpirationController from '../../../components/TokenExpirationController';
import LoginLayout from '../../../layouts/LoginLayout';
import LoginContent from '../../../layouts/LoginLayout/LoginContent';
import TokenExpired from '../SignInPage/TokenExpired';
import ChangePassword, { TranslationType } from './ChangePassword';
import RequestNewPassword from './RequestNewPassword';
import SmsOtp from './SmsOtp';

type State =
    | { step: 'requestNewPassword' }
    | { step: 'resetPassword'; token: string; email?: string }
    | { step: 'smsOtp'; token: string; email?: string };

type Action =
    | { type: 'moveToSmsOTP'; token: string; email?: string }
    | { type: 'moveToAfterSmsOTPSuccess'; token: string; email?: string };

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'moveToSmsOTP':
            return { step: 'smsOtp', token: action.token, email: action.email };

        case 'moveToAfterSmsOTPSuccess':
            return { step: 'resetPassword', token: action.token, email: action.email };

        default:
            return state;
    }
};

export type ActionHandlers = {
    moveToSmsOTP: (token: string, email?: string) => void;
    moveToAfterSmsOTPSuccess: (token: string, email?: string) => void;
};

const ResetPasswordPage = () => {
    const locationState = useLocation().state as { token?: string; verified?: boolean; email?: string };
    const navigate = useNavigate();

    const initialState: State = useMemo(() => {
        if (!isBoolean(locationState?.verified) && !locationState?.token) {
            return { step: 'requestNewPassword' };
        }

        if (locationState.verified) {
            return { step: 'resetPassword', token: locationState.token, email: locationState.email };
        }

        return { step: 'smsOtp', token: locationState.token, email: locationState.email };
    }, [locationState]);

    const [state, dispatch] = useReducer(reducer, initialState);

    const actions = useMemo(
        (): ActionHandlers => ({
            moveToSmsOTP: (token: string, email?: string) => dispatch({ type: 'moveToSmsOTP', token, email }),
            moveToAfterSmsOTPSuccess: (token: string, email?: string) =>
                dispatch({ type: 'moveToAfterSmsOTPSuccess', token, email }),
        }),
        [dispatch]
    );

    const stepElement = (() => {
        const tokenExpiredFallback = (
            <LoginContent>
                <TokenExpired onLinkClick={() => navigate('../signIn', { replace: true })} />
            </LoginContent>
        );

        switch (state.step) {
            case 'requestNewPassword':
                return <RequestNewPassword />;

            case 'smsOtp':
                return (
                    <TokenExpirationController fallback={tokenExpiredFallback} token={state.token}>
                        <SmsOtp actions={actions} email={state.email} token={state.token} />
                    </TokenExpirationController>
                );

            case 'resetPassword':
                return (
                    <TokenExpirationController fallback={tokenExpiredFallback} token={state.token}>
                        <ChangePassword
                            email={state.email}
                            token={state.token}
                            translationType={TranslationType.NewPassword}
                        />
                    </TokenExpirationController>
                );

            default:
                return null;
        }
    })();

    return <LoginLayout>{stepElement}</LoginLayout>;
};

export default ResetPasswordPage;
