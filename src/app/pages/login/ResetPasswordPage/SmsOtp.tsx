import { message } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useResendSmsOtpForResetPasswordMutation } from '../../../api/mutations/resendSmsOTPForResetPassword';
import { useValidateSmsOtpForResetPasswordMutation } from '../../../api/mutations/validateSmsOTPForResetPassword';
import LoginContent from '../../../layouts/LoginLayout/LoginContent';
import useHandleError from '../../../utilities/useHandleError';
import { ActionHandlers as SetupPasswordActionHandlers } from '../ActivateUserAccountPage';
import OTPForm, { OTPFormValues } from '../OTPForm';
import { ActionHandlers as ResetPasswordActionHandlers } from './index';

export type ActionHandlers = ResetPasswordActionHandlers | SetupPasswordActionHandlers;

export type SmsOTPProps = { actions: ActionHandlers; token: string; email?: string };

const SmsOtp = ({ actions, token, email }: SmsOTPProps) => {
    const { t } = useTranslation(['signInPage']);
    const [validate] = useValidateSmsOtpForResetPasswordMutation();
    const [resend] = useResendSmsOtpForResetPasswordMutation();

    const onSubmit = useHandleError(
        async ({ code }: OTPFormValues) => {
            message.loading({
                type: 'loading',
                content: t('signInPage:smsOtpStep.submittingMessage'),
                key: 'primary',
                duration: 0,
            });

            const { data } = await validate({ variables: { token, code: code.join('') } }).finally(() => {
                message.destroy('primary');
            });

            if (data?.validateSmsOTPForResetPassword?.verified) {
                actions.moveToAfterSmsOTPSuccess(data.validateSmsOTPForResetPassword.token, email);
            }
        },
        [actions, t, token, validate]
    );

    const resendCode = useCallback(() => {
        resend({ variables: { token } })
            .then(({ data }) => actions.moveToSmsOTP(data.resendSmsOTPForResetPassword.token, email))
            .catch(() => message.error(t('otpPage:errors.failedToResend')));
    }, [actions, resend, t, token, email]);

    return (
        <LoginContent description={t('signInPage:smsOtpStep.description')} title={t('signInPage:smsOtpStep.title')}>
            <OTPForm handleSubmit={onSubmit} resendCode={resendCode} />
        </LoginContent>
    );
};

export default SmsOtp;
