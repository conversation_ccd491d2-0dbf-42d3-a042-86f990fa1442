type Query {
    """
    Fetch user document for the logged in user, returns null otherwise for anonymous
    """
    currentUser: User

    """
    Get user

    Apply filter inside, so that returned roles/usergroup can be filtered out
    """
    getUser(userId: ObjectID!, filter: UserFilteringRule): User

    """
    List users

    Apply filter inside, so that returned roles/usergroup can be filtered out
    """
    listUsers(pagination: Pagination, filter: UserFilteringRule, sort: UserSortingRule): PaginatedUsers!

    """
    Retrieve a link information
    """
    retrieveLink(id: String!): ExternalLink

    """
    Generate authenticator secret and qrcode
    """
    generateAuthenticatorSetup: AuthenticatorSetup!

    """
    Fetch WebAuthn security keys for a username
    """
    getWebauthnKeys(username: String!): [String!]!

    """
    Generate challenge to authenticate with WebAuthn
    """
    generateAuthenticatorChallenge(username: String!): AuthenticationWithWebPublicKeyCredential

    """
    List banks
    """
    listBanks(pagination: Pagination, sort: BankSortingRule, filter: BankFilteringRule): PaginatedSystemBanks!

    """
    Get a bank by its ID
    """
    getBank(id: ObjectID!): SystemBank

    """
    List bank options
    """
    listBankOptions(sort: BankSortingRule, filter: BankOptionFilteringRule): [BankOption!]!

    """
    Checking whether the uploaded file is safe
    """
    validateFile(upload: Upload!, assetFieldSource: String!): Boolean!
}
